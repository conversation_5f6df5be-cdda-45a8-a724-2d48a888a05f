import argparse
import json
import logging
import os
import re
import subprocess
import sys
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
import time

from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient

"""
Sample Usage:
--------------
To export a model using this script, run the following command:

1. Convert weights from tc to ixf for the given model at falcon_path and export to export_dir and prepare manifest files:
python prepare_snapshot.py --model_name=falcon.orion.d32-s32-k4-fp16rgs-scallion-trimodal \
    --falcon_path=az://path/to/falcon/model \
    --export_dir=folder_name \
    --override force_fp8=True skip_falcon_comparison=False

2. Skip conversion step and copy existing IXF weights to export_dir:
python prepare_snapshot.py --model_name=falcon.orion.d32-s32-k4-fp16rgs-scallion-trimodal \ 
    --falcon_path=az://path/to/falcon/model \
    --export_dir=folder_name \
    --skip_conversion=True

3. Convert TC to IXF and validate the exported model snapshot against the base model snapshot:
python prepare_snapshot.py --model_name=falcon.orion.d32-s32-k4-fp16rgs-scallion-trimodal \
    --falcon_path=az://path/to/falcon/model \
    --export_dir=folder_name \
    --ixf_weights_path_model_name=az://path/to/base/model/weights \
    --ignore_validation_errors=False \
    # In case you want to ignore few files in the validation, you can use the --ignore_validation_error_key_regex flag:
    --ignore_validation_error_key_regex="(fingerprint|guid|filename)"
"""

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
                logging.StreamHandler(sys.stdout),
             ],
)
logger = logging.getLogger(__name__)

ALIAS = os.getenv("OPENAI_USER")
if not ALIAS:
    logger.error("OPENAI_USER environment variable is not set. Please set it to your alias.")
    sys.exit(1)

MANIFEST_FILE_NAME = "manifest.json"
PARTITION_MANIFEST_FILE_NAME = "partition_manifest.json"
METADATA_FILE_NAME = "prepare_snapshot_metadata.json"
AZURE_SCHEME = "az://"
EXPORT_BASE_PATH = f"az://orngtransfer/devaultoutbound/models/{ALIAS}"

# TODO: Add more model configurations as needed
MODEL_CONFIG_MAPPING = {
    "falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal": {
        "model": "falcon.orion.d64-s32-k4-fp16rgs-scallion-trimodal n_op_shards=8 pipe_depth=4 "
                 "tensorcache_v2_load_allow_missing_tensor=True skip_tensors_by_regex_on_initial_load=embedding.image_encoder.* "
                 "allow_embedding_prefix_loading=True "
                 "sequence_parallel_transformer_pipe=False enable_tensorcache_v2=True",
        "root": "twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False "
                "deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 "
                "setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 "
                "create_model_timeout=1080000 step_timeout=1080000 initial_step_timeout=1080000 "
                "download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000 ban_nodes= "
                "enable_wandb_hook=False enable_snowflake_hook=False",
        "skip_falcon_comparison": "True",
        "falcon_comparison_tolerance": "0.075",
        "force_fp8": "False",
        "skip_dataset_specific_info": "True",
        "skip_snapshot_comparison": "True",
        "override_ev3_mm_defaults": "True",
    },
    "falcon.orion.d32-s32-k4-fp16rgs-scallion-trimodal": {
        "model": "falcon.orion.d32-s32-k4-fp16rgs-scallion-trimodal n_op_shards=2 pipe_depth=2 "
                 "tensorcache_v2_load_allow_missing_tensor=True skip_tensors_by_regex_on_initial_load=embedding.image_encoder.* "
                 "sequence_parallel_transformer_pipe=False",
        "root": "twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False "
                "deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 "
                "setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 "
                "create_model_timeout=1080000 step_timeout=1080000 initial_step_timeout=1080000 "
                "download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000 ban_nodes= "
                "enable_wandb_hook=False enable_snowflake_hook=False",
        "skip_falcon_comparison": "True",
        "falcon_comparison_tolerance": "0.075",
        "force_fp8": "False",
        "skip_dataset_specific_info": "True",
        "skip_snapshot_comparison": "True",
        "override_ev3_mm_defaults": "True",
    },
    "falcon.orion.d32-s32-k4-bf16-scallion-trimodal": {
        "model": "falcon.orion.d32-s32-k4-bf16-scallion-trimodal n_op_shards=4 pipe_depth=1 "
                 "tensorcache_v2_load_allow_missing_tensor=True skip_tensors_by_regex_on_initial_load=embedding.image_encoder.* "
                 "sequence_parallel_transformer_pipe=False",
        "root": "twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False "
                "deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 "
                "setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 "
                "create_model_timeout=1080000 step_timeout=1080000 initial_step_timeout=1080000 "
                "download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000 ban_nodes= "
                "enable_wandb_hook=False enable_snowflake_hook=False",
        "skip_falcon_comparison": "True",
        "falcon_comparison_tolerance": "0.075",
        "force_fp8": "False",
        "skip_dataset_specific_info": "True",
        "skip_snapshot_comparison": "True",
        "override_ev3_mm_defaults": "True",
    },
    "falcon.orion.d16-s32-k4-bf16-scallion-trimodal": {
        "model": "falcon.orion.d16-s32-k4-bf16-scallion-trimodal n_op_shards=2 pipe_depth=1 optim_pipe_depth=1 "
                 "restore_parent_state=params,unembedding n_replicas=4 sequence_parallel_transformer_pipe=False "
                 "twppo.scallion.common twppo.dembed twppo.fetch_params_inline mask_masked_tokens_dust_decoder=False "
                 "attn_global_scale_param=exp moe_global_scale_param=exp",
        "root": "twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False "
                "deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 "
                "setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 "
                "create_model_timeout=1080000 step_timeout=1080000 initial_step_timeout=1080000 "
                "download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000 ban_nodes= "
                "enable_wandb_hook=False enable_snowflake_hook=False",
        "force_fp8": "False",
        "override_ev3_mm_defaults": "True",
    },
    "falcon.multimodal.runs.scallion-d36-s64-lpe": {
        "model": "falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False "
                 "twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 "
                 "ixf_kv_block_unit_size=2048 n_op_shards=4 pipe_depth=2 n_replicas=1",
        "root": "twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False "
                "deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 "
                "setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 "
                "create_model_timeout=1080000  step_timeout=1080000 initial_step_timeout=1080000 "
                "download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000 ban_nodes=  "
                "enable_wandb_hook=False enable_snowflake_hook=False",
        "skip_falcon_comparison": "True",
        "falcon_comparison_tolerance": "0.075",
        "force_fp8": "False",
        "override_ev3_mm_defaults": "True",
        "skip_dataset_specific_info": "True",
    },
    "swe-agent-mini-2": {
        "model": "falcon.multimodal.runs.scallion-d36-s64-lpe raise_on_load_for_missing_tensor=False "
                 "twppo.scallion.text.common enable_tensorcache_v2=False ixf_max_cache_list_length=4096 "
                 "ixf_kv_block_unit_size=2048 n_op_shards=8 pipe_depth=1 n_replicas=1",
        "root": "twppo.dev enable_pod_eviction_hook=False tee_out_to_container=False tee_out_to_azure=False "
                "deduplicate_ray_logs=False boot_timeout=108000 rpc_timeout=108000 connect_timeout=108000 "
                "setup_profiling_timeout=108000 create_dataset_timeout=108000 create_model_comms_timeout=108000 "
                "create_model_timeout=1080000  step_timeout=1080000 initial_step_timeout=1080000 "
                "download_checkpoint_timeout=108000 upload_checkpoint_timeout=108000 ban_nodes=  "
                "enable_wandb_hook=False enable_snowflake_hook=False",
        "skip_falcon_comparison": "True",
        "falcon_comparison_tolerance": "0.075",
        "force_fp8": "False",
        "override_ev3_mm_defaults": "True",
        "skip_dataset_specific_info": "True",
    },
}


def get_container_client(export_dir: str):
    """
    Get the Azure Blob Storage container client for the given export directory.
    :param export_dir: The export directory in the format az://<account_name>/<container_name>/<prefix>.        
    :return: BlobServiceClient for the specified container.
    """
    if not export_dir.startswith(AZURE_SCHEME):
        raise ValueError(
            f"Unsupported scheme in model_root_dir: {export_dir}. Only 'az://' is supported."
        )
    parsed_url = urlparse(export_dir)

    account_name = parsed_url.netloc.split(".")[0]
    container_name = parsed_url.path.split("/")[1]
    prefix = "/".join(parsed_url.path.split("/")[2:])

    # Initialize Azure Blob Storage client
    blob_service_client = BlobServiceClient(
        account_url=f"https://{account_name}.blob.core.windows.net",
        credential=DefaultAzureCredential(),
    )
    container_client = blob_service_client.get_container_client(container_name)
    return container_client, prefix


def run_export_inference(
    model_name: str, falcon_path: str, export_dir: str, overrides: dict = None, new_model: bool = False
):
    """
    Wrapper function to run the export_inference.py script with the resolved configuration.

    :param model_name: The name of the model to export.
    :param falcon_path: Path to the source model weights.
    :param export_dir: Folder name to export the model.
    :param overrides: A dictionary of arguments to override the defaults.
    """

    if new_model:
        config = {}
    elif model_name in MODEL_CONFIG_MAPPING:
        # Get the default configuration for the model and apply overrides if provided
        config = MODEL_CONFIG_MAPPING[model_name]
    elif model_name not in MODEL_CONFIG_MAPPING:
        logger.error(f"Model '{model_name}' is not defined in the configuration mapping. If you want to convert a model that is not defined in the mapping, use the --new_model flag.")
        raise ValueError(f"Model '{model_name}' is not defined in the configuration mapping. If you want to convert a model that is not defined in the mapping, use the --new_model flag.")

    if overrides:
        config.update(overrides)

    # Construct the command to run export_inference.py
    command = [
        "python",
        "/root/code/glass/personal/lixiaoli/mopub/export_inference.py",
        f"--falcon_path={falcon_path}",
        f"--export_dir={export_dir}",
        f"--add_unique_prefix=False",
        f"--report_to_slack=False",
        "--skip_snapshot_comparison=True",
        "--skip_snapshot_format_verification=True",
    ]
    for key, value in config.items():
        command.append(f"--{key}={value}")

    logger.info("Running command:")
    logger.info(" ".join(command))

    subprocess.run(command, check=True)


def list_azure_files(container_client, prefix: str) -> List[str]:
    """List all files in an Azure Blob Storage container with a given prefix."""
    blob_list = container_client.list_blobs(name_starts_with=prefix)
    return [blob.name for blob in blob_list]


def read_blob_file(container_client, blob_name: str) -> Optional[str]:
    """Read the content of a blob in Azure Blob Storage."""
    try:
        blob_client = container_client.get_blob_client(blob_name)
        blob_data = blob_client.download_blob()
        return blob_data.content_as_text()
    except Exception as e:
        logger.error(f"Error reading blob {blob_name}: {e}", exc_info=True)
        raise


def generate_manifests(
    model_root_dir: str, model_dataset_path: str, tc_format: bool = False
) -> None:
    """Generate manifests for model registration and deployment.

    :param model_root_dir: Root directory of the converted IXF model.
    :param model_dataset_path: Path where the model artifacts live relative to the root of the HOBO storage container.
    """
    container_client, prefix = get_container_client(model_root_dir)
    logger.info(f"Manifest file creation started.")

    files = list_azure_files(container_client, prefix)
    base_path = os.path.join("{*}", model_dataset_path)
    base_path = base_path.replace("\\", "/")
    storage_items = []
    layers = []
    common_files = []

    for file in files:
        if (
            not file.endswith(".json")
            and not file.endswith(".storage")
            and not file.endswith(MANIFEST_FILE_NAME)
            and not file.endswith(".tc")
            and not file.endswith(".checkpoint_complete")
        ):
            logger.info(f"Found irrelevant file: {file}")
            continue
        remote_location = os.path.join("{*}", file)
        remote_location = remote_location.replace("\\", "/")
        storage_items.append(
            {
                "RemoteLocation": remote_location,
                "localRelativePath": os.path.basename(file),
                "Unpack": 0,
            }
        )
        if tc_format:
            match = re.match(
                r"checkpoint_replica(\d+)_depth(\d+)_shard(\d+)_expert(\d+)_expertgroup(\d+).tc",
                file,
            )
        else:
            match = re.match("resblocks\.(\d+)\.part_\d+\..+\.storage", file)
        if match:
            if not tc_format:
                layer = int(match.group(1))
            else:
                layer = int(match.group(2))  # tc format does not have layer, use depth instead
            while len(layers) <= layer:
                layers.append({"files": [], "index": len(layers)})
            layers[layer]["files"].append(remote_location)
        else:
            common_files.append(remote_location)

    manifest = {
        "storageItems": storage_items,
        # NOTE 9/20 -- It seems like partition manifest breaks multi-LoRA and is also not
        # required for deployments of merged weights? Commenting out for now.
        # TODO: remove all partition manifest logic from code if this continues to be the case.
        # "modelPartitionManifest": os.path.join(base_path, PARTITION_MANIFEST_FILE_NAME)
    }
    partition_manifest = {
        "version": "v1",
        "matchingType": "exact",
        "commonFiles": common_files,
        "layers": layers,
    }

    # Write manifests to Azure Blob Storage
    manifest_blob_client = container_client.get_blob_client(
        os.path.join(prefix, MANIFEST_FILE_NAME)
    )
    partition_manifest_blob_client = container_client.get_blob_client(
        os.path.join(prefix, PARTITION_MANIFEST_FILE_NAME)
    )

    logger.info(f"Writing to {model_root_dir=} {MANIFEST_FILE_NAME=}")
    manifest_blob_client.upload_blob(json.dumps(manifest), overwrite=True)

    logger.info(f"Writing to {model_root_dir=} {PARTITION_MANIFEST_FILE_NAME=}")
    partition_manifest_blob_client.upload_blob(json.dumps(partition_manifest), overwrite=True)

    logger.info(f"Manifest file creation completed.")


def identify_snapshot_paths(all_ixf_files: List[str]) -> List[Optional[str]]:
    """Identify base model snapshot file from a list of IXF paths."""
    # based on _resolve_weights_file_name function in azureoai_entrypoint.py
    snapshot_file_name_patterns = [
        "snapshot_path[.]json",  # code-cushman-002
        "8k-snapshot_fixed[.][0-9a-fA-F]+[.]json",  # code-cushman-002-fixed
        "snapshot[.]32k[.][0-9a-fA-F]+[.]json",  # devault 32k snapshot
        "snapshot[.][0-9a-fA-F]+[.]json",  # default ixf snapshot file
        ".+snapshot[.][0-9a-fA-F]+[.]json",  # default ixf snapshot file with a prefix
        "snapshot[.]msft-prod[.][0-9a-fA-F]+[.]json",  # ixf snapshot file pattern for gpt-35/gpt-4
        "clover-os4[.][0-9a-fA-F]+[.]json",  # gpt-41-nano model
    ]

    snapshot_files = []
    for file_name_pattern in snapshot_file_name_patterns:
        snapshot_files.extend([entry for entry in all_ixf_files if re.match(file_name_pattern, os.path.basename(entry))])
    logging.info(f"multiple snapshots are detected. {snapshot_files=}")
    return snapshot_files


def read_snapshot_file(base_model_root_dir: str) -> Dict[str, Any]:
    """Identify and read the snapshot file for the given base model root directory.

    :param base_model_root_dir: Root directory of the base IXF model
    """
    container_client, prefix = get_container_client(base_model_root_dir)
    logger.info(f"Searching snapshot file from {base_model_root_dir} with prefix {prefix}")

    # Identify snapshot file
    ixf_ckpt_files = list_azure_files(container_client, prefix)
    # logger.info(f"Found IXF checkpoint files: {ixf_ckpt_files}")
    snapshot_files = identify_snapshot_paths(ixf_ckpt_files)
    if not snapshot_files:
        logger.error(f"No snapshot files found in {base_model_root_dir}.")
        raise FileNotFoundError(f"No snapshot files found in {base_model_root_dir}.")
    if len(snapshot_files) > 1:
        logger.warning(
            f"Multiple snapshot files found in {base_model_root_dir}. Using the first one: {snapshot_files[0]}"
        )
    snapshot_file = snapshot_files[0]

    # Read snapshot file
    snapshot_data = read_blob_file(container_client, snapshot_file)
    try:
        return json.loads(snapshot_data)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to decode JSON from snapshot file {snapshot_file}: {e}")
        raise ValueError(f"Failed to decode JSON from snapshot file {snapshot_file}: {e}")


def generate_metadata(export_dir: str, start_time: float, end_time: float, model_name: str, falcon_path: str, overrides: dict, new_model: bool = False):
    container_client, prefix = get_container_client(export_dir)
    logger.info(f"Metadata file creation started.")

    metadata_blob_client = container_client.get_blob_client(
        os.path.join(prefix, METADATA_FILE_NAME)
    )
    if new_model:
        config = {}
    else: 
        config = MODEL_CONFIG_MAPPING[model_name]
    if overrides:
        config.update(overrides)

    # Retrieve all files matching "snapshot.*.json"
    snapshot_files = []
    blob_list = container_client.list_blobs(name_starts_with=prefix)
    for blob in blob_list:
        if re.match(r".*snapshot\..*\.json$", blob.name) and ".params." not in blob.name:
            snapshot_files.append(os.path.basename(blob.name))

    # Write metadata to a file in the export directory
    metadata = {
        "first_timestamp": time.strftime("%Y-%m-%dT%H:%M:%S", time.gmtime(start_time)),
        "end_time": time.strftime("%Y-%m-%dT%H:%M:%S", time.gmtime(end_time)),
        "time_spent_seconds": (end_time - start_time),
        "model_name": model_name,
        "folder": prefix,
        "config": config,
        "source_path": falcon_path,
        "snapshot_files": snapshot_files,
    }

    try:
        metadata_blob_client.upload_blob(json.dumps(metadata), overwrite=True)
        logger.info(f"Metadata written to {os.path.join(prefix, METADATA_FILE_NAME)}")
    except Exception as e:
        logger.error(f"Failed to write metadata file: {e}")


def copy_weights_to_export_dir(falcon_path: str, export_dir: str):
    """
    Copy weights from the Falcon path to the export directory in Azure Blob Storage.

    :param falcon_path: Path to the Falcon model.
    :param export_dir: Directory to export the model.
    """
    try:
        # Construct the bbb sync command
        command = ["bbb", "sync", falcon_path, export_dir]

        # Log the command being executed
        logger.info(f"Running command: {' '.join(command)}")

        # Execute the command
        subprocess.run(command, check=True)

        logger.info("Successfully copied IXF format files to export directory.")
    except subprocess.CalledProcessError as e:
        logger.error(f"Copy command failed with error: {e}")
        sys.exit(1)


def flatten_json(y, prefix=''):
    out = {}

    def flatten(x, name=''):
        if isinstance(x, dict):
            for a in x:
                flatten(x[a], f"{name}{a}__")
        elif isinstance(x, list):
            for i, a in enumerate(x):
                flatten(a, f"{name}[{i}]__")
        else:
            out[name[:-2]] = x
    flatten(y, prefix)
    return out


def compare_json_dicts(dict1, dict2):
    flat1 = flatten_json(dict1)
    flat2 = flatten_json(dict2)

    all_keys = set(flat1.keys()).union(flat2.keys())
    differences = []

    for key in all_keys:
        val1 = flat1.get(key, '<MISSING>')
        val2 = flat2.get(key, '<MISSING>')
        if val1 != val2:
            differences.append((key, val1, val2))

    return differences


def validated_exported_model_snapshot_path(
    base_model_blob_path: str,
    exported_model_blob_path: str,
    ignore_validation_errors: bool = False,
    ignore_validation_error_key_regex: str = None
) -> None:
    """
    Validate the exported model snapshot against the base model snapshot.

    :param base_model_blob_path: Path to the base model snapshot in IXF format.
    :param exported_model_blob_path: Path to the exported model snapshot.
    :param ignore_validation_errors: If True, ignore validation errors and proceed.
    """
    logger.info("Validating exported model snapshot against base model snapshot.")

    # Read base model snapshot
    base_snapshot_json = read_snapshot_file(base_model_blob_path)

    # Read exported model snapshot
    exported_snapshot_json = read_snapshot_file(exported_model_blob_path)

    # Compare
    differences = compare_json_dicts(base_snapshot_json, exported_snapshot_json)

    # Filter out differences based on regex pattern if provided
    filtered_differences = []
    for key, val1, val2 in differences:
        should_ignore = False
        if ignore_validation_error_key_regex:
            if re.search(ignore_validation_error_key_regex, key, re.IGNORECASE):
                should_ignore = True
                logger.info(f"Ignoring validation error for key matching regex pattern: {key}")

        if not should_ignore:
            filtered_differences.append((key, val1, val2))
            logging.warning(f"{key} | (b){val1} | (ft){val2}")
        else:
            logging.info(f"Skipped due to regex: {key} | (b){val1} | (ft){val2}")

    if filtered_differences:
        if ignore_validation_errors:
            logger.warning("Validation errors found but ignored due to --ignore_validation_errors flag.")
        else:
            logger.error("Validation errors found in exported model snapshot. Please check the differences above.")
            raise ValueError("Validation errors found in exported model snapshot.")

    logger.info("Exported model snapshot validation successful.")


if __name__ == "__main__":

    parser = argparse.ArgumentParser(
        description="Prepare snapshot after converting from tc to IXF format or copying existing IXF weights to export_dir."
    )
    parser.add_argument(
        "--model_name",
        type=str,
        required=True,
        help="The name of the model to export."
    )
    # NOTE Add model and region based mapping directly in the script.
    parser.add_argument(
        "--ixf_weights_path_model_name",
        type=str,
        default=None,
        required=False,
        help="Path to base model weights in IXF format. If provided, it will be used to diff snapshot files of exported falcon model with base model weights."
    )
    parser.add_argument(
        "--ignore_validation_errors",
        type=str,
        default="false",
        required=False,
        help="Ignore validation errors during snapshot comparison."
    )
    parser.add_argument(
        "--ignore_validation_error_key_regex",
        type=str,
        default="(fingerprint|guid|filename)",
        required=False,
        help="Ignore validation errors for specific keys matching the provided regex pattern. "
             "Default skips keys containing 'fingerprint', 'guid', or 'filename'."
    )
    parser.add_argument(
        "--falcon_path", 
        type=str, 
        required=True, 
        help="Path to the source model weights."
    )
    parser.add_argument(
        "--export_dir",
        type=str,
        required=True,
        help="Folder name to export the model under az://orngtransfer/devaultoutbound/models/<alias>/",
    )
    parser.add_argument(
        "--override", 
        type=str, 
        nargs="*", 
        help="Override specific arguments (key=value)."
    )
    parser.add_argument(
        "--skip_conversion",
        type=bool,
        default=False,
        help="Skip conversion step if weights are already in ixf format and move them to export_dir.",
    )
    parser.add_argument(
        "--new_model",
        type=bool,
        required=False,
        default=False,
        help="Convert a model name that is not defined in the configuration mapping. Overrides are necessary to define the model config."
    )

    args = parser.parse_args()

    overrides = {}
    if args.override:
        for override in args.override:
            key, value = override.split("=", 1)
            overrides[key] = value

    if not args.falcon_path.startswith(AZURE_SCHEME):
        logger.error("Falcon path should be a valid Azure Blob Storage path starting with az://")
        sys.exit(1)

    if args.export_dir.startswith(AZURE_SCHEME):
        logger.error(
            "Export directory should be a relative path to az://orngtransfer/devaultoutbound/models/<alias>/"
        )
        sys.exit(1)

    if args.new_model and not args.override:
        logger.error("When using --new_model flag, you must provide overrides to define the model config for conversion.")
        sys.exit(1)

    if "/" in args.export_dir.strip('/'):
        logger.error("Export directory should be the name of the folder to export the model relative to az://orngtransfer/devaultoutbound/models/<alias>/. Do not include sub folders.")
        sys.exit(1)

    export_dir = f"{EXPORT_BASE_PATH}/{args.export_dir.strip('/')}"

    container_client, prefix = get_container_client(export_dir)

    # Check if export_dir already exists
    blob_list = container_client.list_blobs(name_starts_with=prefix)
    if any(blob_list):
        logger.error(f"The export directory '{export_dir}' already exists in the blob storage. Please provide a different export_dir.")
        sys.exit(1)

    # log start time
    start_time = time.time()

    # If weights already in IXF, skip conversion step and directly copy weights to export directory
    if args.skip_conversion:
        copy_weights_to_export_dir(args.falcon_path, export_dir)
    else:
        run_export_inference(args.model_name, args.falcon_path, export_dir, overrides, args.new_model)

    # Validate exported model
    if args.ixf_weights_path_model_name is not None:
        validated_exported_model_snapshot_path(
            base_model_blob_path=args.ixf_weights_path_model_name,
            exported_model_blob_path=export_dir,
            ignore_validation_errors=args.ignore_validation_errors.lower() == "true",
            ignore_validation_error_key_regex=args.ignore_validation_error_key_regex
        )

    # generate manifest files in export directory
    generate_manifests(export_dir, f"models/{ALIAS}/{args.export_dir.strip('/')}")

    end_time = time.time() 
    generate_metadata(export_dir, start_time, end_time, args.model_name, args.falcon_path, overrides, args.new_model)

    logger.info(f"Your output is generated at: {export_dir}")
