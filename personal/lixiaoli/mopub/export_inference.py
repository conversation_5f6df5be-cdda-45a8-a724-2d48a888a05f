#!/usr/bin/env python
"""Convert a torchflow checkpoint to IXF.

Converts model defined in the `model` configuration to inference model and export it.
NOTE: if your job hangs, try `b restart --container <jobname>` to restart the job.

For instance, use `gpt4-d4` to export a gpt4 d4 model.
Specify snapshot to export via `load_finetune="<path>"`.
By default snapshot is exported with fp8 precision (--force-fp8=True flag).

For the latest on how to export d120's and sahara models for ChatGPT see http://go/mainline_model_export

Limitations:
    - Exporting is currently done only with api_moe_kernel_version=v2.

If you cannot run the commands you may have to run `./install.py model_export_scripts`.

Note 1: LoRA export

    To export a LoRA model, add "attn_lora_dim=X always_fully_initialize_model=True to the model config",
    along with a lora_base_guid for the base ixf id. This will only export the LoRA weights.
    (only non-KV LoRA configured with attn_lora_dim is supported now)

Example 1: Exporting gpt4-d4 to "/tmp/export" from devbox.

    export MODEL="gpt4-d4 n_op_shards=2 pipe_depth=2"
    python -m model_export_scripts.export_inference --model="$MODEL" --falcon_path="az://oaici/torchflow/snapshots/d4-v4"

    # To encrypt the exported model, add --encrypt_dir=<path> to the command above.
    # You might also need to specify --encrypt_target (defaults to "deploy").
    # You may also specify --encryption_type_str (defaults to None which uses default set by applied_models.tools.encrypt_remote_model).
    # See go/enc-rollout for details.


Example 2: Launching export job.

    export MODEL="gpt4-d4 n_op_shards=2 pipe_depth=2"
    python -m model_export_scripts.export_inference --model="$MODEL" --falcon_path="az://oaici/torchflow/snapshots/d4-v4" --cluster=owl --team=rl --num_machines=1 --force=True --name=export-inference

Example 3: Exporting gpt4-d120 from devbox:

    # NOTE: You might need between 10-20 pipe depth depending on the type of the GPU you are using.
    export MODEL="gpt4-d120 n_op_shards=8 pipe_depth=10"
    export EXPORT_DIR="az://oaiscalingft/ppo-v1/harmony/kostrikov_ofl-june26_runs-d120-ppo-07-04--05-05/train_step_000520/policy.ixf_v2"
    python -m model_export_scripts.export_inference --model="$MODEL" --falcon_path="az://oaiscalingft/ppo-v1/harmony/kostrikov_ofl-june26_runs-d120-ppo-07-04--05-05/train_step_000520/policy/" --export_dir=$EXPORT_DIR

Example 4: A small test to verify all pieces are working.
export MODEL="falcon.gpt4.d16-v4 n_op_shards=8 pipe_depth=1"
python -m model_export_scripts.export_inference --model="$MODEL" --falcon_path="az://oainest/scaling/colang-v2-series/adrien-6g-colang-v2-series-d16-ep2/elapsed_tokens_000000027722906758/22061800532873JDY6IT-1/" --export_dir="/tmp/export_dir" --encrypt_dir="/tmp/export_dir.encrypt" --export_reshard=True --report_to_slack=False


"""

import functools
import json
import logging
import threading
import os
from dataclasses import dataclass
from typing import TYPE_CHECKING, Literal, Mapping, Optional, Sequence

import blobfile as bf
import torch

import clusters
import twapi
from applied_falcon.ixf_activation_dtype_opts import ActivationDtypeOpts
from applied_models.tools.constants import ENCRYPTION_DIR_PREFIX, SUPPORTED_AUTO_COPY_DESTINATIONS
from applied_models.tools.slack_utils import post_slack_message
from chat import render
from clusters import storage_map
from falcon.model_config import RunConfig
from sciclone_utils import operations as sciclone_operations
from scope import trace
from torchflow.common.logging_configure import logging_configure
from twapi.rpcs import export_inference_rpc
from twapi.rpcs.large_test_vector import TEST_LARGE_VECTOR_QUESTION_STR

if TYPE_CHECKING:
    from applied_models.gpt.config import DatasetSpecificInfo

logger = logging.getLogger(__name__)

EV3_MM_DEFAULTS = {
    "image_encoder_type": ("emb", lambda c: c.model_config.vision_config.image_encoder_type),
    "allow_sampling_vit_to_emb": (
        True,
        lambda c: c.model_config.manual_transform_tc_config.allow_sampling_vit_to_emb,
    ),
    "skip_tensors_by_regex_on_initial_load": (
        "(embedding\\.image_encoder\\.clip|embedding\\.audio_encoder).*",
        lambda c: c.skip_tensors_by_regex_on_initial_load,
    ),
    "do_multimodal_projections_in_ixf_batch_runner": (
        True,
        lambda c: c.do_multimodal_projections_in_ixf_batch_runner,
    ),
}


@dataclass
class Args:
    model: str
    # path to the falcon snapshot.
    falcon_path: str
    # location to put
    export_dir: str
    # We don't need to tee to azure, this might run into issues on whale right now
    # and we have the logs in the pod. `name` is needed to record intermediate falcon activations.
    root: str = "twppo.dev  name=ixf_export enable_snowflake_hook=False enable_wandb_hook=False"

    # Skip falcon comparison. Highly recommended to keep this as False.
    skip_falcon_comparison: bool = False
    soft_fail_falcon_comparison: bool = False
    # if set to None, we will dynamically set tol based on the model size.
    # deeper models have high tol.
    falcon_comparison_tolerance: float | None = None
    # Choice of test vector for falcon comparison.
    test_vector_type: Literal["text", "multimodal", "text-large"] = "text"

    # Skip snapshot comparison. Highly recommended to keep this as False.
    skip_snapshot_comparison: bool = False
    # Using tiny tolerance because we are comparing test vectors generated by the same model.
    snapshot_comparison_tolerance: float = 5e-3

    # Skip snapshot format verification. Highly recommended to keep this as False.
    skip_snapshot_format_verification: bool = False

    # Whether to use dedicated embed and unembed when exporting the model.
    dedicated_embed: bool = False

    # Convert to fp8 regardless whether the original model is fp16 or not.
    # Recommended since fp8 is more efficient for inference and test vectors cannot be captured
    # from the original falcon model with fp8 precision (when "resblock_weight_dtype=float8").
    #
    # TODO: Remove this flag once we can capture test vectors from fp8 falcon model
    #  and instead use "twppo.fp8" or similar.
    force_fp8: bool = True
    # String like map i.e "resblock_internal_dtype=fp16" to override the activation dtype to be parsed by
    # ActivationDtypeOpts.parse_arguments()
    activation_dtype_override: str | None = None

    # Optionally allow the model to be also exported to 16 shards if exported model is ixf format and 8 shards.
    export_reshard: bool = False

    # LoRA arguments.
    lora_base_guid: str | None = None

    # Encryption arguments.
    # Note: `encrypt_dir` must be specified to enable encryption.
    encrypt_dir: str | None = None
    # Warning: prod_deprecated will use the old keyvault that many people have access to, so you should avoid using it for new models!
    encrypt_target: Literal[
        "deploy",
        "prod_deprecated",
        "staging_deprecated",
        "prod",
        "staging",
        "msft",
        "unit_test",
    ] = "deploy"
    # Encryption type to use. See go/enc-rollout for more details, options[None, "cross_shards_encryption", "per_shard_encryption"].
    # Optional[Literal] type is not allowed per launcher.py
    encryption_type_str: str | None = None
    # When copy_to_prod is True, the encrypted snapshot will be copied to transfer storage account, and auto-replication will make it available on the api side.
    copy_to_prod: bool = False
    # This would be name of the directory that will be used in Storage account for the weights transfer process and will be used to form a snapshot_id.
    # example, if snapshot_name is "gpt4-d4-2021-09-15", the snapshot_id will be "gpt4-d4-2021-09-15-internal-2023-09-15-12-00-00". Snapshot_id will be have the suffix "-internal-<transfer_date>".
    snapshot_name: str | None = None
    # This is the path to the snapshot overrides file. This file will be used to override the snapshot config.
    snapshot_overrides_file: str | None = None
    # This is the description of the snapshot. This is passed to admin-openai tool.
    description: str | None = None
    # Override the context size of the model when serving. If set to None, use the default value in the source config.
    n_ctx: int | None = 32768

    # If True, slack messages might be sent to #snapshot-transfer.
    report_to_slack: bool = True

    # If true we will test the test_vectors for all possible ixf batch sizes only used for verify.
    test_all_ixf_batch_sizes: bool = False

    # Skip get_dataset_specific_info, used for exporting pre-training ckpts.
    skip_dataset_specific_info: bool = False

    # Only export image_encoder or audio_encoder as IXF.
    # This is useful for running HybridClip encoder for scallion/orion.
    encoder_only: bool = False
    encoder_modality: Literal["audio", "image"] = "image"
    add_unique_prefix: bool = True

    # By default, use the attention implementation in the snapshot checkpoint file.
    # This will override any attention implementation specified in the model str.
    override_checkpoint_attention_implementation: bool = True

    # If False, the ev3 multimodal flags will not be allowed be overriden.
    override_ev3_mm_defaults: bool = False

    def __post_init__(self) -> None:
        if self.activation_dtype_override is None and not self.force_fp8:
            logger.warning(
                "CAUTION: Should have activation_dtype_override or force_fp8 for optimized inference. Contact Applied Inference (Model Runners) team for help if you're planning to run this on prod"
            )

        if self.activation_dtype_override is not None and self.force_fp8:
            # TODO: This would be better served by an argument that specifies the desired "inference time quantization policy" and makes decisions
            #       based on that. But `force_fp8` is too common right now
            raise ValueError(
                "Export for inference does not support both specifying activation_dtype_override and having force_fp8=True"
            )


def flatten_classifiers(classifiers: Mapping[str, Mapping[str, int]]) -> dict[str, int]:
    result: dict[str, int] = {}
    for taxonomy in classifiers.values():
        for lower_value, id in taxonomy.items():
            result[f"{lower_value}"] = id
    return result


def get_dataset_specific_info(load_finetune: str) -> Optional["DatasetSpecificInfo"]:
    # This is a hack to make CI tests pass. This information is not present in the models used at CI.
    if "az://oaici/" in load_finetune:
        return None

    from applied_models.gpt.config import ConvoClassifierInfo, DatasetSpecificInfo
    from harmony_checkpoint_utils import checkpoint_utils

    if not load_finetune:
        return None

    renderer = render.get_renderer(
        **checkpoint_utils.renderer_kwargs_from_ckpt_dir(ckpt_dir=load_finetune)
    )
    cc: ConvoClassifierInfo | None = None
    classifier = renderer.get_convo_classifier()
    if classifier is None:
        logger.info("No classifier found in renderer")
    else:
        classes: dict[str, int] = flatten_classifiers(classifiers=classifier.classifiers)
        cc = ConvoClassifierInfo(
            classification_token=classifier.classification_token, classes=classes
        )
    return DatasetSpecificInfo(harmony_renderer_name=renderer.name, convo_classifier=cc)


def _check_ev3_mm_flags(config: RunConfig, override_ev3_mm_defaults: bool) -> None:
    for key, (expected_value, getattr_fn) in EV3_MM_DEFAULTS.items():
        try:
            config_value = getattr_fn(config)  # type: ignore[no-untyped-call]
        except AttributeError:
            config_value = None
        if config_value != expected_value:
            err_msg: str = f"Key {key} mismatch! Expected {expected_value}, found {config_value}! "
            err_msg += "It will not work with Engine-v3. Please use 'twppo.ev3.mm.defaults' to override these flags."
            if override_ev3_mm_defaults:
                logger.warning(
                    f"\033[91m{err_msg}\nExport will continue, but model may not work with Engine-v3.\033[0m"
                )
            else:
                logger.error(
                    f"\033[91m{err_msg}\nIf you really need this config and understand the consequence, "
                    f"pass --override_ev3_mm_defaults to allow overriding.\033[0m"
                )
                raise ValueError(err_msg)


def torchflow_to_ixf(args: Args, export_dir: str) -> tuple[twapi.TwModel, str, str]:
    """Converts the torchflow model to an IXF and writes it to az://.

    Args:
        export_dir: location to write ixf to.

    Returns:
        model: The TWAPI model
        model_config: Model config used in string form.
        snapshot_path: Output location of the IXF.
    """
    import tensorcache.local as tc
    from applied_models.tools.snapshot_config_utils import make_snapshot_override_raw_config

    cp = tc.CheckpointReader(args.falcon_path)

    config = twapi.join_config(
        args.model,
        "twppo.sampling_only",
        "twppo.linear",
        load_finetune=args.falcon_path,
        api_moe_kernel_version="v2",
        dataset_client_side=True,
        enable_print_hook=False,
        create_sampling_comm=True,
        eval_only=True,
        warmup_forward=False,
        dedicated_embed=args.dedicated_embed,
        dedicated_unembed=args.dedicated_embed,
        # One replica is enough for export.
        n_replicas=1,
        stochastic_rounding_allow_partial=True,  # This must be set when bst attention is used.
        # We use float32 for exports which uses torch ops.
        torch_error_on_cublas_workspace_creation=False,
        use_torch_matmul_for_float32=True,
        dp_collective_ops_use_tree=False,
    )

    if args.override_checkpoint_attention_implementation:
        cp_config = cp.get_metadata()["run_config"]["model_config"]
        # NOTE: Forcing specific attention implementation, so it matches the
        #      implementation used for the verification in verify_load_test_vector.py.
        config = twapi.join_config(
            config,
            attention_implementation=cp_config["attention_implementation"],
        )

    # Parse/validate dtype arguments
    act_dtype_opts: ActivationDtypeOpts | None = None
    if args.activation_dtype_override is not None:
        act_dtype_opts = ActivationDtypeOpts.parse_arguments(args.activation_dtype_override)

    if args.force_fp8:
        # Typically to enable fp8 export we need to set "twppo.fp8",
        # which includes "out_weight_dtype=float16". While we cannot use
        # "twppo.fp8" (because "resblock_weight_dtype=float8" prevent us from
        # capturing test vectors from falcon model), we can still use
        # "out_weight_dtype=float16" directly.
        config = twapi.join_config(config, "out_weight_dtype=float16")

    config = twapi.join_config(config, "mask_masked_tokens_dust_decoder=False")
    config = twapi.join_config(config, "dynamic_esharding_softmax_after_dropping=False")

    snapshot_override_values = (
        make_snapshot_override_raw_config(args.snapshot_overrides_file)
        if args.snapshot_overrides_file is not None
        else None
    )
    # Export inference model.
    close_timeout_sec = 60
    builder = twapi.Builder(args.root)
    try:
        model: twapi.TwModel = builder.model(label="export", config=config)
        twapi.assert_model_config_for_inference(run_config=model.config)
        if "falcon.gpt4.d4" not in config and "falcon.gpt4v" not in config:
            # Not supposed to support gpt4/4v
            _check_ev3_mm_flags(model.config, args.override_ev3_mm_defaults)

        info = model.metric_model_info  # MetricModelInfo
        assert info
        model_config = info.base_model_config  # str

        # Verifying and supporting bf16 models
        if (_backbone_act_dtype := model.config.model_config.backbone_act_dtype) == torch.bfloat16:
            if args.force_fp8:
                # This is being enforced in this "end user" script. The in-memory and python functions will still run as usual
                raise ValueError(
                    f"--force_fp8=True has not been validated for models with backbone_act_dtype={_backbone_act_dtype}, please use --force_fp8=False for now"
                )
            if act_dtype_opts is None:
                assert args.activation_dtype_override is None, (
                    f"Can't have {act_dtype_opts=} be None if {args.activation_dtype_override=} is present"
                )
                act_dtype_opts = ActivationDtypeOpts(resblock_internal_dtype=torch.float16)
                logger.warning(
                    f"Since this model has backbone_act_dtype={_backbone_act_dtype}, and no specific activation dtypes override were set, we're setting the recommended value from Applied Inference (Model Runners) for most optimal inference (not necessarily the most bitwise equivalent setting): {act_dtype_opts}"
                )

        possible_snapshot_paths = get_ixf_snapshots(export_dir)
        if len(possible_snapshot_paths) > 1:
            raise ValueError(
                f"Expected at most 1 snapshot in {export_dir}, found {possible_snapshot_paths}"
            )
        snapshot_path: str | None = (
            possible_snapshot_paths[0] if len(possible_snapshot_paths) > 0 else None
        )
        if snapshot_path is not None:
            logger.info("Found existing snapshot. Skipping conversion.")
            return model, model_config, snapshot_path

        builder.commit()

        if args.test_vector_type == "text":
            tokens = model.encoding.encode(export_inference_rpc.TEST_VECTOR_QUESTION_STR) + (
                [model.encoding.eot_token]
            )
        elif args.test_vector_type == "multimodal":
            tokens = json.loads(export_inference_rpc.TEST_VECTOR_MM_TOKENS_JSON) + [
                model.encoding.eot_token
            ]
        elif args.test_vector_type == "text-large":
            tokens = model.encoding.encode(TEST_LARGE_VECTOR_QUESTION_STR)
        else:
            raise ValueError(f"Unexpected test vector type: {args.test_vector_type}")

        # Decoupled unembedding need to pad to 128, othwise n_op_shards
        block_size = max(128, model.config.n_op_shards)
        tokens += [model.encoding.eot_token] * (block_size - (len(tokens) % block_size))

        # Max amount of tokens allowed in the forward pass
        tokens = tokens[: min(len(tokens), 2048)]
        if args.export_reshard and (
            model.config.ixf_implementation != "ixf" or model.config.n_op_shards != 8
        ):
            raise ValueError(
                "16 shard export is only supported for models exported to ixf with 8 shards."
            )

        if args.falcon_comparison_tolerance is None:
            # for now, we will special case tol based on the model type.
            # small models should be fine with 2**-4 (0.06) which is the default.
            # bigger models still have some error though. This error should ideally be tracked
            # down but for now we are using higher tol.
            falcon_comparison_tolerance = 2**-4
            if "d120" in model_config:
                falcon_comparison_tolerance = 0.1
            elif "d48-dust" in model_config:
                falcon_comparison_tolerance = 0.075
        else:
            falcon_comparison_tolerance = args.falcon_comparison_tolerance
        logger.info(
            "Found model config for setting tol. Found: %s, using falcon_comparison_tolerance=%f",
            model_config,
            falcon_comparison_tolerance,
        )

        if args.n_ctx and model.config.ixf_implementation == "ixf":
            # n_ctx only supported for ixf models.
            if snapshot_override_values is not None:
                # use the provided n_ctx value to override the snapshot config.
                snapshot_override_values["n_ctx"] = args.n_ctx
            else:
                snapshot_override_values = {"n_ctx": args.n_ctx}

        with trace.trace("export_inference"):
            assert model.config.load_finetune
            snapshot_paths = model.export_inference(
                label="export",
                export_dir=export_dir,
                skip_falcon_comparison=args.skip_falcon_comparison,
                soft_fail_falcon_comparison=args.soft_fail_falcon_comparison,
                force_fp8=args.force_fp8,
                lora_base_guid=args.lora_base_guid,
                tol=falcon_comparison_tolerance,
                tokens=tokens,
                dataset_specific_info=(
                    None
                    if args.skip_dataset_specific_info
                    else get_dataset_specific_info(model.config.load_finetune)
                ),
                snapshot_override_values=snapshot_override_values,
                act_dtype_opts=act_dtype_opts,
                encoder_only=args.encoder_only,
                encoder_modality=args.encoder_modality,
            )
            assert len(snapshot_paths) == 1, "Expected only one snapshot path"
            snapshot_path = snapshot_paths[0]
            pass
        logger.info("Successfully exported inference model to: %s", snapshot_path)
    

    # TODO(lmetz): This really shouldn't need to return the torchflow model.
    return model, model_config, snapshot_path


def get_ixf_snapshots(ixf_dir: str) -> Sequence[str]:
    """This method returns all possible 'snapshot' looking files under a path"""
    logger.info("Checking if ixf exists: %s", ixf_dir)
    if not bf.exists(ixf_dir):
        logger.info("IXF does not exist: %s", ixf_dir)
        return []

    files: Sequence[str] = list(bf.glob(bf.join(ixf_dir, "snapshot.*.json")))
    # filter out the snapshot with .params.
    files = [f for f in files if ".params." not in f]
    if len(files) == 0:
        logger.info("IXF seems to be partially written (no snapshot json files): %s", ixf_dir)

    return files


def _is_ixf_encrypted_export_present(encrypt_dir: str, encrypt_target: str) -> bool:
    if encrypt_target == "deploy":
        # TODO: This logic is too hard coded

        unified_path = bf.join(encrypt_dir, ENCRYPTION_DIR_PREFIX + ".all")
        if bf.exists(unified_path):
            # Unified logic
            possible_snapshots: Sequence[str] = get_ixf_snapshots(unified_path)
            if len(possible_snapshots) != len(SUPPORTED_AUTO_COPY_DESTINATIONS):
                logger.info(
                    f"Expected {len(SUPPORTED_AUTO_COPY_DESTINATIONS)} snapshots, found: {possible_snapshots=}"
                )
                return False
            else:
                return True
        else:
            # Non-unified logic
            for destination in SUPPORTED_AUTO_COPY_DESTINATIONS:
                single_dest_path = bf.join(encrypt_dir, ENCRYPTION_DIR_PREFIX + "." + destination)
                if not bf.exists(single_dest_path):
                    logger.info(f"Destination doesn't exist yet: {single_dest_path}")
                    return False

                possible_snapshots = get_ixf_snapshots(single_dest_path)
                if len(possible_snapshots) != 1:
                    logger.info(f"Expected 1 snapshots, found: {possible_snapshots=}")
                    return False
            return True
    else:
        logger.warning(
            f"Unsupported encrypt target {encrypt_target}. Assuming not encrypted and will re-run encryption. This will take extra time."
        )
        return False


def main(unique_prefix: str, args: Args) -> None:
    # delay import so this module loads on a macbook.
    from applied_falcon.export import (
        encrypt_snapshot_and_copy_to_prod,
        reshard_16ways,
        verify_snapshot_test_vectors,
    )
    from applied_models.tools.snapshot_utils import assert_snapshot_name_format
    from applied_models.tools.verify_well_formed_snapshot import verify_well_formed_snapshot

    encrypt_target: str = args.encrypt_target

    logging_configure()
    trace.init_trace("/tmp/", rsync_trace_from_host=True)

    logger.info("Arguments:")
    logger.info(str(args))
    if "load_finetune" in args.model:
        raise ValueError(
            "load_finetune should no longer be passed into model! use --falcon_path instead"
        )

    if args.copy_to_prod:
        assert args.snapshot_name, "Please provide a snapshot name to copy to prod"
        # Check if the Snapshot name can form valid directory name.
        assert_snapshot_name_format(args.snapshot_name)
        assert args.description, "Please provide a description for the snapshot."

    # We can read into remote clusters but only in a read only way, when we write out we must jump
    # to the cluster we are running on.
    # for now, to keep existing patterns working, let's write to the corresponding cluster matching the filename.
    # This is a bit of a footgun because now the clusters being written too don't match where the original files are.
    # TODO(lmetz): Once the company has a good distributed filesystem story update this.
    export_dir = (
        bf.join(args.export_dir, unique_prefix) if args.add_unique_prefix else args.export_dir
    )
    if not export_dir.startswith("/"):
        storage_set, path_suffix = storage_map.get_storage_set_name_from_path(export_dir)
        export_dir = os.path.join(
            sciclone_operations.get_or_create_storage_set(storage_set),
            path_suffix.removeprefix("/"),
        )

    encrypt_dir: str | None
    if args.encrypt_dir:
        encrypt_dir = (
            bf.join(args.encrypt_dir, unique_prefix) if args.add_unique_prefix else args.encrypt_dir
        )
        # check if file is not local.
        if clusters.is_blob_path(encrypt_dir):
            storage_set, path_suffix = storage_map.get_storage_set_name_from_path(encrypt_dir)
            encrypt_dir = os.path.join(
                sciclone_operations.get_or_create_storage_set(storage_set),
                path_suffix.removeprefix("/"),
            )
    else:
        encrypt_dir = None

    logger.info(f"Regionalizing write dirs. {export_dir=} {encrypt_dir=}")

    logger.info(f"Regionalizing falcon path. Before: {args.falcon_path=}")
    args.falcon_path = sciclone_operations.sync_regionally(args.falcon_path)
    logger.info(f"Regionalizing falcon path. After: {args.falcon_path=}")

    with trace.trace("torchflow_to_ixf"):
        model, model_config, snapshot_path = torchflow_to_ixf(args, export_dir=export_dir)

    # Verify exported inference by loading it and checking test vectors.
    if args.skip_snapshot_comparison:
        logger.warning("Skipping test vector comparison with the exported inference model.")
    else:
        assert not args.encoder_only, (
            "capture and verification of test_vectors is not supported for encoder only usages"
        )
        logger.info(
            "Comparing exported test vectors with test vectors generated "
            "by inference model loaded from snapshot."
        )
        with trace.trace("verify_snapshot_test_vectors"):
            verify_snapshot_test_vectors(
                model.config,
                snapshot_path,
                tolerance=args.snapshot_comparison_tolerance,
                test_all_ixf_batch_sizes=args.test_all_ixf_batch_sizes,
                # There are some issues with the cuda graphs that are not yet resolved.
                # This does not affect the correctness of the export, it only affects the check at export time.
                # Snapshots run with ev3/zen will not have this issue.
                skip_cuda_graphs=True,
            )

    # Verify format of exported weights and configs.
    if args.skip_snapshot_format_verification:
        logger.warning("Skipping verification of the format of exported weights and configs.")
    else:
        assert not args.encoder_only, (
            "verification of snapshot format is not supported for encoder only usages"
        )
        logger.info("Verifying the format of exported weights and configs.")
        with trace.trace("verify_well_formed_snapshot"):
            verify_well_formed_snapshot(
                snapshot_path,
                digests_required=True,
                include_test_vectors=True,
                verify_encrypted=None,
                verify_n_shards=model.config.n_op_shards,
            )

    # Encrypt snapshot.
    if encrypt_dir is not None:
        if not _is_ixf_encrypted_export_present(encrypt_dir, encrypt_target):
            if bf.exists(encrypt_dir):
                logger.info(
                    "Deleting existing encrypted snapshot since probably malformed: %s", encrypt_dir
                )
                bf.rmtree(encrypt_dir)
                bf.rmdir(encrypt_dir)
                assert not bf.exists(encrypt_dir), "Blobfile should have deleted the dir!"

            with trace.trace("encrypt_snapshot_and_copy_to_prod"):
                assert model.config.load_finetune
                encrypt_snapshot_and_copy_to_prod(
                    model.config.n_op_shards,
                    snapshot_path,
                    encrypt_dir,
                    target=encrypt_target,
                    encryption_type_str=args.encryption_type_str,
                    copy_to_prod=args.copy_to_prod,
                    snapshot_name=args.snapshot_name,
                    description=args.description,
                    base_model_config=model_config,
                    finetune_path=args.falcon_path,
                )
            logger.info("Successfully encrypted snapshot to: %s", encrypt_dir)
        else:
            logger.info("Already encrypted. Skipping this step.")
    else:
        # TODO: we should enable automatic copying to prod for un-encrypted weights too.
        logger.info("Skipping encryption of the snapshot.")
        msg = "To encrypt the model, please run the following command:"
        logger.info(f"\033[91m {msg} \033[0m")
        cmd_msg = f"python -m applied_models.tools.launch_encrypt_job launch --snapshot_path {snapshot_path} --dest_path {export_dir.removesuffix('/') + '.encrypted'} --cluster=owl --use_git=False --team=rl --use_gpu_machines --n_shards {model.config.n_op_shards} --torchflow_path={model.config.load_finetune} --base_model_config={model_config} --num_machines=<num_machines>"
        logger.info(f"\033[92m {cmd_msg} \033[0m")
        if args.report_to_slack:
            post_slack_message(msg=msg, command=cmd_msg)

    if args.export_reshard:
        shard16_dest_path = f"{export_dir.removesuffix('/')}.16shard"
        logger.info("Exporting 16 way shard to %s", shard16_dest_path)
        shard16_snapshot_name = f"{args.snapshot_name}-16shard" if args.snapshot_name else None
        # Hard coding this number to 6 nodes, 8 gpu a piece (6*8=48).
        # TODO(lmetz): Should probably safely scale with number machines being used to shard.
        # This count also must be a multiple of 16.
        gpus_for_reshard = 48

        possible_shard16_snapshot_paths: Sequence[str] = get_ixf_snapshots(shard16_dest_path)
        if len(possible_shard16_snapshot_paths) > 1:
            raise ValueError(
                f"Expected at most 1 snapshot in {shard16_dest_path}, found {possible_shard16_snapshot_paths}"
            )
        shard16_snapshot_path: str | None = (
            possible_shard16_snapshot_paths[0] if len(possible_shard16_snapshot_paths) > 0 else None
        )

        if shard16_snapshot_path is None:
            if bf.exists(shard16_dest_path):
                logger.info(
                    "Deleting existing 16 shard snapshot since probably malformed: %s",
                    shard16_dest_path,
                )
                bf.rmtree(shard16_dest_path)
                bf.rmdir(shard16_dest_path)
                assert not bf.exists(shard16_dest_path), "Blobfile should have deleted the dir!"

            try:
                with trace.trace("reshard_16ways"):
                    shard16_snapshot_path = reshard_16ways(
                        snapshot_path=snapshot_path,
                        dest_path=shard16_dest_path,
                        mpi_gpu=gpus_for_reshard,
                    )
            except Exception as e:
                logger.error(f"Failed to reshard snapshot to 16 shards: {e}")
                msg = "To reshard 16 ways, please run the following command in devbox with enough pods to hold the model (update command as needed):"
                logger.info(f"\033[91m {msg} \033[0m")
                cmd_msg = f"brix mpi -N {gpus_for_reshard} -- python -m applied_models.tools.reshard --snapshot_path {snapshot_path} --n_shards 16 --dest_path {shard16_dest_path}"
                logger.info(f"\033[92m {cmd_msg} \033[0m")
                if args.report_to_slack:
                    post_slack_message(msg=msg, command=cmd_msg)
                raise e

        logger.info("Found existing snapshot %s. Skipping conversion.", shard16_snapshot_path)

        if encrypt_dir:
            shard16_encrypt_dir = f"{encrypt_dir.removesuffix('/')}.16shard"
            if not _is_ixf_encrypted_export_present(shard16_encrypt_dir, encrypt_target):
                if bf.exists(shard16_encrypt_dir):
                    logger.info("Deleting existing encrypted snapshot: %s", shard16_encrypt_dir)
                    bf.rmtree(shard16_encrypt_dir)
                    bf.rmdir(shard16_encrypt_dir)
                    assert not bf.exists(shard16_encrypt_dir), (
                        "Blobfile should have deleted the dir!"
                    )

                logger.info("Encrypting 16 way shard to %s", shard16_dest_path)
                with trace.trace("encrypt_snapshot_and_copy_to_prod:16way"):
                    assert model.config.load_finetune
                    encrypt_snapshot_and_copy_to_prod(
                        16,
                        shard16_snapshot_path,
                        shard16_encrypt_dir,
                        target=encrypt_target,
                        encryption_type_str=args.encryption_type_str,
                        copy_to_prod=args.copy_to_prod,
                        snapshot_name=shard16_snapshot_name,
                        description=args.description,
                        base_model_config=model_config,
                        finetune_path=model.config.load_finetune,
                    )
                logger.info("Successfully encrypted 16 shard snapshot to: %s", shard16_encrypt_dir)
            else:
                logger.info("Already encrypted 16 shard. Skipping this step.")

        else:
            logger.info("Skipping encryption of the 16 shard snapshot.")
            msg = (
                "To encrypt the model, please run the following command (update command as needed):"
            )
            logger.info(f"\033[91m {msg} \033[0m")
            shard16_encrypt_dir = "az://path/to/your/encrypt/dir.16shard"
            cmd_msg = f"python -m applied_models.tools.launch_encrypt_job launch --snapshot_path {shard16_snapshot_path} --dest_path {shard16_encrypt_dir} --cluster=owl --use_git=False --team=rl --use_gpu_machines --n_shards 16 --torchflow_path={model.config.load_finetune} --base_model_config={model_config} --num_machines=<num_machines>"
            logger.info(f"\033[92m {cmd_msg} \033[0m")
            if args.report_to_slack:
                post_slack_message(msg=msg, command=cmd_msg)
    logger.info("Done exporting. Exiting main")


if __name__ == "__main__":
    import datetime

    from oai_launch import launch

    # We launch with date time prefix computed BEFORE the job launches.
    # This ensures that only one version of the code can be used to make an export.
    unique_prefix = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")

    launch.launchable(functools.partial(main, unique_prefix), use_dataclass=True, setup_twapi=True)
